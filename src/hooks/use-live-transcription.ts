import type {
  AudioSource,
  LiveTranscriptionState,
  LiveTranscriptionConfig,
  LiveTranscriptionSegment,
  UseLiveTranscriptionReturn,
} from 'src/types';

import { v4 as uuidv4 } from 'uuid';
import { useRef, useMemo, useState, useCallback } from 'react';

import { useGetLiveTranscriptionJwtMutation } from 'src/store/api/live-transcription/hooks';

import { toast } from 'src/components/snackbar';

// Optimized constants for better performance
const RECORDING_SAMPLE_RATE = 16_000;
const SPEECHMATICS_RT_URL = 'wss://eu2.rt.speechmatics.com/v2';
const BUFFER_SIZE = 4096; // Reduced for lower latency
const AUDIO_WORKLET_URL = '/audio-worklet-processor.js';
const BUFFER_FLUSH_INTERVAL = 100; // ms - for low latency

export const useLiveTranscription = (): UseLiveTranscriptionReturn => {
  const [getLiveTranscriptionJwt] = useGetLiveTranscriptionJwtMutation();

  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedAudioDeviceId, setSelectedAudioDeviceId] = useState<string>('');
  const audioPermissionGrant = useRef<boolean>(false);

  const segmentsRef = useRef<LiveTranscriptionSegment[]>([]);
  const websocketRef = useRef<WebSocket | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // Dual stream refs
  const speakerWebsocketRef = useRef<WebSocket | null>(null);
  const microphoneWebsocketRef = useRef<WebSocket | null>(null);
  const speakerMediaStreamRef = useRef<MediaStream | null>(null);
  const speakerAudioContextRef = useRef<AudioContext | null>(null);
  const speakerProcessorRef = useRef<ScriptProcessorNode | null>(null);
  const speakerWorkletNodeRef = useRef<AudioWorkletNode | null>(null);
  const speakerSourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // Add buffering for better performance
  const audioBufferRef = useRef<ArrayBuffer[]>([]);
  const speakerAudioBufferRef = useRef<ArrayBuffer[]>([]);
  const bufferFlushIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const speakerBufferFlushIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const metricsUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioLevelRef = useRef<number>(0);
  const speakerAudioLevelRef = useRef<number>(0);

  // Add performance monitoring
  const performanceMetricsRef = useRef({
    audioPacketsSent: 0,
    audioPacketsLost: 0,
    averageLatency: 0,
    lastPacketTime: 0,
  });

  // Add quality monitoring
  const qualityMetricsRef = useRef({
    silenceThreshold: 0.01,
    isSpeaking: false,
    backgroundNoiseLevel: 0,
  });

  const [state, setState] = useState<LiveTranscriptionState>({
    isConnected: false,
    isRecording: false,
    isTranscribing: false,
    segments: [],
    error: null,
    connectionState: 'disconnected',
    performanceMetrics: {
      audioPacketsSent: 0,
      audioPacketsLost: 0,
      averageLatency: 0,
    },
    qualityMetrics: {
      isSpeaking: false,
      backgroundNoiseLevel: 0,
    },
    isDualMode: false,
    dualStream: {
      isCapturingScreen: false,
      speakerConnected: false,
      microphoneConnected: false,
      speakerConnectionState: 'disconnected',
      microphoneConnectionState: 'disconnected',
      speakerError: null,
      microphoneError: null,
    },
  });

  // Memoized audio constraints for better performance
  const audioConstraints = useMemo(
    () => ({
      sampleRate: RECORDING_SAMPLE_RATE,
      channelCount: 1,
      echoCancellation: true, // Keep for microphone - removes echo
      noiseSuppression: true, // Good for microphone - removes background noise
      autoGainControl: true, // Good for microphone - normalizes volume
      latency: 0, // Request lowest latency
    }),
    []
  );

  const speakerConstraints = useMemo(
    () => ({
      sampleRate: RECORDING_SAMPLE_RATE,
      channelCount: 1,
      echoCancellation: false, // Don't alter screen audio
      noiseSuppression: false, // Preserve original screen audio quality
      autoGainControl: false, // Don't auto-adjust screen audio levels
      suppressLocalAudioPlayback: false,
    }),
    []
  );

  // Optimized buffer flushing with performance tracking
  const flushAudioBuffer = useCallback(() => {
    // In dual mode, use microphoneWebsocketRef; in single mode, use websocketRef
    const targetWebSocket = microphoneWebsocketRef.current || websocketRef.current;

    if (audioBufferRef.current.length > 0 && targetWebSocket?.readyState === WebSocket.OPEN) {
      const startTime = performance.now();

      // Send all buffered audio data
      for (const buffer of audioBufferRef.current) {
        targetWebSocket.send(buffer);
        performanceMetricsRef.current.audioPacketsSent++;
      }

      // Update latency metrics
      const endTime = performance.now();
      const latency = endTime - startTime;
      performanceMetricsRef.current.averageLatency =
        (performanceMetricsRef.current.averageLatency + latency) / 2;
      performanceMetricsRef.current.lastPacketTime = endTime;

      audioBufferRef.current = [];
    }
  }, []);

  // Speaker buffer flushing for dual stream mode
  const flushSpeakerAudioBuffer = useCallback(() => {
    if (
      speakerAudioBufferRef.current.length > 0 &&
      speakerWebsocketRef.current?.readyState === WebSocket.OPEN
    ) {
      for (const buffer of speakerAudioBufferRef.current) {
        speakerWebsocketRef.current.send(buffer);
      }

      speakerAudioBufferRef.current = [];
    }
  }, []);

  const updateSegments = useCallback(
    (newSegment: LiveTranscriptionSegment, audioSource?: AudioSource) => {
      // Set audio source if provided
      if (audioSource) {
        newSegment.audioSource = audioSource;
      }

      if (newSegment.isFinal) {
        // For final segments, add to the list
        segmentsRef.current = [...segmentsRef.current, newSegment];
      } else {
        // For partial segments, replace the last partial segment from the same source or add new one
        const finalSegments = segmentsRef.current.filter((segment) => segment.isFinal);
        const otherPartialSegments = segmentsRef.current.filter(
          (segment) => !segment.isFinal && segment.audioSource !== audioSource
        );
        segmentsRef.current = [...finalSegments, ...otherPartialSegments, newSegment];
      }

      setState((prev) => ({
        ...prev,
        segments: segmentsRef.current,
      }));
    },
    []
  );

  const cleanupAudioResources = useCallback(() => {
    // Clear buffer flush interval
    if (bufferFlushIntervalRef.current) {
      clearInterval(bufferFlushIntervalRef.current);
      bufferFlushIntervalRef.current = null;
    }

    // Clear metrics update interval
    if (metricsUpdateIntervalRef.current) {
      clearInterval(metricsUpdateIntervalRef.current);
      metricsUpdateIntervalRef.current = null;
    }

    // Clear audio buffer
    audioBufferRef.current = [];

    if (workletNodeRef.current) {
      workletNodeRef.current.disconnect();
      workletNodeRef.current = null;
    }
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }
    if (sourceRef.current) {
      sourceRef.current.disconnect();
      sourceRef.current = null;
    }
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach((track) => track.stop());
      mediaStreamRef.current = null;
    }
  }, []);

  const cleanupWebSocket = useCallback(() => {
    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = null;
    }
  }, []);

  // Dual stream WebSocket setup
  const setupDualWebSocket = useCallback(
    (jwt: string, config: LiveTranscriptionConfig, audioSource: AudioSource) =>
      new Promise<void>((resolve, reject) => {
        try {
          const ws = new WebSocket(`${SPEECHMATICS_RT_URL}?jwt=${jwt}`);
          ws.binaryType = 'arraybuffer';

          ws.onopen = () => {
            console.log(`WebSocket connected to Speechmatics for ${audioSource}`);

            // Send StartRecognition message
            const startMessage = {
              message: 'StartRecognition',
              audio_format: {
                type: 'raw',
                encoding: 'pcm_f32le',
                sample_rate: RECORDING_SAMPLE_RATE,
              },
              transcription_config: {
                language: config.language,
                diarization: 'speaker',
                operating_point: 'enhanced',
                max_delay_mode: 'flexible',
                max_delay: 1,
                enable_partials: true,
                enable_entities: true,
                speaker_diarization_config: {
                  max_speakers: 50,
                },
              },
            };

            ws.send(JSON.stringify(startMessage));
          };

          ws.onmessage = (event) => {
            try {
              const message = JSON.parse(event.data);
              switch (message.message) {
                case 'RecognitionStarted':
                  setState((prev) => ({
                    ...prev,
                    dualStream: {
                      ...prev.dualStream!,
                      ...(audioSource === 'speaker'
                        ? { speakerConnected: true, speakerConnectionState: 'connected' }
                        : { microphoneConnected: true, microphoneConnectionState: 'connected' }),
                    },
                    isTranscribing: true,
                  }));
                  resolve();
                  break;

                case 'AddPartialTranscript':
                  if (message.metadata?.transcript) {
                    const speakerId = message.results?.find(
                      (result: any) => result.alternatives?.[0]?.speaker
                    )?.alternatives?.[0]?.speaker;

                    const segment: LiveTranscriptionSegment = {
                      id: uuidv4(),
                      text: message.metadata.transcript,
                      confidence: 0.8,
                      startTime: message.metadata.start_time || Date.now() / 1000,
                      endTime: message.metadata.end_time || Date.now() / 1000,
                      isFinal: false,
                      speakerId: speakerId || undefined,
                      timestamp: new Date(),
                      audioSource,
                    };
                    updateSegments(segment, audioSource);
                  }
                  break;

                case 'AddTranscript':
                  if (message.metadata?.transcript) {
                    const avgConfidence = message.results?.length
                      ? message.results.reduce(
                          (sum: number, result: any) =>
                            sum + (result.alternatives?.[0]?.confidence || 0),
                          0
                        ) / message.results.length
                      : 0.9;

                    const speakerId = message.results?.find(
                      (result: any) => result.alternatives?.[0]?.speaker
                    )?.alternatives?.[0]?.speaker;

                    const segment: LiveTranscriptionSegment = {
                      id: uuidv4(),
                      text: message.metadata.transcript,
                      confidence: avgConfidence,
                      startTime: message.metadata.start_time || Date.now() / 1000,
                      endTime: message.metadata.end_time || Date.now() / 1000,
                      isFinal: true,
                      speakerId: speakerId || undefined,
                      timestamp: new Date(),
                      audioSource,
                    };
                    updateSegments(segment, audioSource);
                  }
                  break;

                case 'AudioAdded':
                  // Audio chunk was processed successfully - don't spam console
                  break;

                case 'Error':
                  console.error(`Speechmatics error for ${audioSource}:`, message);
                  setState((prev) => ({
                    ...prev,
                    dualStream: {
                      ...prev.dualStream!,
                      ...(audioSource === 'speaker'
                        ? { speakerConnectionState: 'error', speakerError: message.reason }
                        : { microphoneConnectionState: 'error', microphoneError: message.reason }),
                    },
                  }));
                  reject(new Error(message.reason || 'Speechmatics error'));
                  break;

                case 'Warning':
                  console.warn(`Speechmatics warning for ${audioSource}:`, message);
                  break;

                case 'Info':
                  // Handle Info messages (usually status updates) - don't spam console
                  if (message.reason && !message.reason.includes('transcriber_idle')) {
                    console.log(`Speechmatics info for ${audioSource}:`, message.reason);
                  }
                  break;

                default:
                  // Only log unknown messages occasionally to avoid spam
                  if (Math.random() < 0.1) {
                    // Log only 10% of unknown messages
                    console.log(
                      `Unknown Speechmatics message for ${audioSource}:`,
                      message.message
                    );
                  }
                  break;
              }
            } catch (error) {
              console.error(`Error parsing Speechmatics message for ${audioSource}:`, error);
            }
          };

          ws.onerror = (error) => {
            console.error(`WebSocket error for ${audioSource}:`, error);
            setState((prev) => ({
              ...prev,
              dualStream: {
                ...prev.dualStream!,
                ...(audioSource === 'speaker'
                  ? { speakerConnectionState: 'error', speakerError: 'WebSocket connection error' }
                  : {
                      microphoneConnectionState: 'error',
                      microphoneError: 'WebSocket connection error',
                    }),
              },
            }));
            reject(new Error('WebSocket connection error'));
          };

          ws.onclose = (event) => {
            console.log(`WebSocket closed for ${audioSource}:`, event.code, event.reason);
            setState((prev) => ({
              ...prev,
              dualStream: {
                ...prev.dualStream!,
                ...(audioSource === 'speaker'
                  ? { speakerConnected: false, speakerConnectionState: 'disconnected' }
                  : { microphoneConnected: false, microphoneConnectionState: 'disconnected' }),
              },
            }));
          };

          // Store WebSocket reference
          if (audioSource === 'speaker') {
            speakerWebsocketRef.current = ws;
          } else {
            microphoneWebsocketRef.current = ws;
          }
        } catch (error) {
          reject(error);
        }
      }),
    [updateSegments]
  );

  const setupAudioCapture = useCallback(async () => {
    try {
      const constraints = selectedAudioDeviceId
        ? { ...speakerConstraints, deviceId: { exact: selectedAudioDeviceId } }
        : audioConstraints;

      const stream = await navigator.mediaDevices.getUserMedia({ audio: constraints });

      mediaStreamRef.current = stream;

      // Create audio context with optimized settings
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: RECORDING_SAMPLE_RATE,
        latencyHint: 'interactive', // Optimize for low latency
      });
      audioContextRef.current = audioContext;

      // Create audio source from microphone
      const source = audioContext.createMediaStreamSource(stream);
      sourceRef.current = source;

      // Try to use AudioWorklet for better performance
      try {
        await audioContext.audioWorklet.addModule(AUDIO_WORKLET_URL);

        // Create worklet node
        const workletNode = new AudioWorkletNode(audioContext, 'audio-worklet-processor');
        workletNodeRef.current = workletNode;

        // Handle audio data from worklet
        workletNode.port.onmessage = (event) => {
          if (event.data.type === 'audioData') {
            // Update audio level if provided
            if (typeof event.data.audioLevel === 'number') {
              audioLevelRef.current = event.data.audioLevel;

              // Update quality metrics
              const isSpeaking = event.data.audioLevel > qualityMetricsRef.current.silenceThreshold;
              qualityMetricsRef.current.isSpeaking = isSpeaking;

              // Track background noise when not speaking
              if (!isSpeaking) {
                qualityMetricsRef.current.backgroundNoiseLevel =
                  (qualityMetricsRef.current.backgroundNoiseLevel + event.data.audioLevel) / 2;
              }
            }

            // In dual mode, use microphoneWebsocketRef; in single mode, use websocketRef
            const targetWebSocket = microphoneWebsocketRef.current || websocketRef.current;

            // Send audio data directly if WebSocket is ready, otherwise buffer it
            if (targetWebSocket?.readyState === WebSocket.OPEN) {
              targetWebSocket.send(event.data.data);
            } else {
              // Buffer audio data for later transmission (will be flushed by interval)
              audioBufferRef.current.push(event.data.data);
            }
          }
        };

        // Connect audio source to worklet
        source.connect(workletNode);

        // Worklet sends data directly to WebSocket, NO need for buffer interval
        console.log('🎤 Audio worklet setup completed');
      } catch (workletError) {
        console.warn(
          'AudioWorklet not supported, falling back to ScriptProcessorNode:',
          workletError
        );

        // Fallback to ScriptProcessorNode with optimized buffer size
        const processor = audioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
        processorRef.current = processor;

        processor.onaudioprocess = (event) => {
          // In dual mode, use microphoneWebsocketRef; in single mode, use websocketRef
          const targetWebSocket = microphoneWebsocketRef.current || websocketRef.current;

          if (targetWebSocket?.readyState === WebSocket.OPEN) {
            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Calculate audio level for monitoring
            let sum = 0;
            for (let i = 0; i < inputData.length; i++) {
              sum += inputData[i] * inputData[i];
            }
            audioLevelRef.current = Math.sqrt(sum / inputData.length);

            // Convert Float32Array to ArrayBuffer for Speechmatics
            const buffer = new ArrayBuffer(inputData.length * 4);
            const view = new Float32Array(buffer);
            view.set(inputData);

            targetWebSocket.send(buffer);
          }
        };

        source.connect(processor);
        processor.connect(audioContext.destination);

        // ScriptProcessorNode needs interval-based buffer flushing only if using buffer mode
        // (Currently this processor sends directly, so no interval needed here either)
        console.log('🎤 Audio processor setup completed');
      }

      setState((prev) => ({
        ...prev,
        isRecording: true,
      }));

      return audioContext;
    } catch (error) {
      console.error('Error setting up audio capture:', error);
      throw new Error('Failed to access microphone');
    }
  }, [selectedAudioDeviceId, audioConstraints, speakerConstraints]);

  // Setup speaker audio processing for dual stream mode
  const setupSpeakerAudioProcessing = useCallback(async () => {
    try {
      if (!speakerMediaStreamRef.current) {
        throw new Error('No speaker stream available');
      }

      // Create audio context for speaker audio
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: RECORDING_SAMPLE_RATE,
        latencyHint: 'interactive',
      });
      speakerAudioContextRef.current = audioContext;

      // Create audio source from screen capture
      const source = audioContext.createMediaStreamSource(speakerMediaStreamRef.current);
      speakerSourceRef.current = source;

      // Try to use AudioWorklet for better performance
      try {
        await audioContext.audioWorklet.addModule(AUDIO_WORKLET_URL);

        // Create worklet node for speaker audio
        const workletNode = new AudioWorkletNode(audioContext, 'audio-worklet-processor');
        speakerWorkletNodeRef.current = workletNode;

        // Handle audio data from speaker worklet
        workletNode.port.onmessage = (event) => {
          if (event.data.type === 'audioData') {
            // Update speaker audio level if provided
            if (typeof event.data.audioLevel === 'number') {
              speakerAudioLevelRef.current = event.data.audioLevel;
            }

            // Buffer speaker audio data for efficient transmission
            speakerAudioBufferRef.current.push(event.data.data);
          }
        };

        // Connect speaker audio source to worklet
        source.connect(workletNode);

        // Start speaker buffer flushing at regular intervals
        speakerBufferFlushIntervalRef.current = setInterval(
          flushSpeakerAudioBuffer,
          BUFFER_FLUSH_INTERVAL
        );
      } catch (workletError) {
        console.warn(
          'AudioWorklet not supported for speaker audio, falling back to ScriptProcessorNode:',
          workletError
        );

        // Fallback to ScriptProcessorNode for speaker audio
        const processor = audioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
        speakerProcessorRef.current = processor;

        processor.onaudioprocess = (event) => {
          if (speakerWebsocketRef.current?.readyState === WebSocket.OPEN) {
            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Calculate speaker audio level for monitoring
            let sum = 0;
            for (let i = 0; i < inputData.length; i++) {
              sum += inputData[i] * inputData[i];
            }
            speakerAudioLevelRef.current = Math.sqrt(sum / inputData.length);

            // Convert Float32Array to ArrayBuffer for Speechmatics
            const buffer = new ArrayBuffer(inputData.length * 4);
            const view = new Float32Array(buffer);
            view.set(inputData);

            speakerWebsocketRef.current.send(buffer);
          }
        };

        source.connect(processor);
        processor.connect(audioContext.destination);
      }

      console.log('Speaker audio processing setup completed');
      return audioContext;
    } catch (error) {
      console.error('Error setting up speaker audio processing:', error);
      throw new Error('Failed to process speaker audio');
    }
  }, [flushSpeakerAudioBuffer]);

  const stopScreenCapture = useCallback(async () => {
    if (speakerMediaStreamRef.current) {
      speakerMediaStreamRef.current.getTracks().forEach((track) => track.stop());
      speakerMediaStreamRef.current = null;
    }

    setState((prev) => ({
      ...prev,
      dualStream: {
        ...prev.dualStream!,
        isCapturingScreen: false,
        speakerConnected: false,
        speakerConnectionState: 'disconnected',
      },
    }));
  }, []);

  const startScreenCapture = useCallback(async () => {
    try {
      setState((prev) => ({
        ...prev,
        dualStream: {
          ...prev.dualStream!,
          speakerConnectionState: 'connecting',
        },
      }));

      // First try with audio
      let screenStream: MediaStream | null = null;

      try {
        screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: 1, height: 1 },
          audio: {
            sampleRate: RECORDING_SAMPLE_RATE,
            channelCount: 1,
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
          },
        });

        console.log('Screen capture stream:', screenStream);
      } catch (audioError) {
        // Check if user cancelled the dialog
        if (
          audioError instanceof Error &&
          (audioError.name === 'NotAllowedError' ||
            audioError.name === 'AbortError' ||
            audioError.message.includes('Permission denied') ||
            audioError.message.includes('cancelled'))
        ) {
          throw new Error('USER_CANCELLED_SCREEN_SHARING');
        }

        console.warn(
          'Screen audio capture not supported, trying without specific constraints:',
          audioError
        );

        // Fallback: try with basic audio constraints
        try {
          screenStream = await navigator.mediaDevices.getDisplayMedia({
            video: { width: 1, height: 1 },
            audio: true,
          });
        } catch (basicAudioError) {
          // Check if user cancelled the dialog
          if (
            basicAudioError instanceof Error &&
            (basicAudioError.name === 'NotAllowedError' ||
              basicAudioError.name === 'AbortError' ||
              basicAudioError.message.includes('Permission denied') ||
              basicAudioError.message.includes('cancelled'))
          ) {
            throw new Error('USER_CANCELLED_SCREEN_SHARING');
          }

          console.warn(
            'Basic screen audio capture not supported, trying video + audio:',
            basicAudioError
          );

          // Fallback: try with video + audio (some browsers require video for audio)
          try {
            screenStream = await navigator.mediaDevices.getDisplayMedia({
              video: {
                width: 1,
                height: 1,
              },
              audio: true,
            });
          } catch (videoAudioError) {
            // Check if user cancelled the dialog
            if (
              videoAudioError instanceof Error &&
              (videoAudioError.name === 'NotAllowedError' ||
                videoAudioError.name === 'AbortError' ||
                videoAudioError.message.includes('Permission denied') ||
                videoAudioError.message.includes('cancelled'))
            ) {
              throw new Error('USER_CANCELLED_SCREEN_SHARING');
            }

            console.error('Screen capture with audio not supported:', videoAudioError);
            throw new Error(
              'Screen audio capture not supported on this browser/system. Please use Chrome 72+ or Firefox 66+ with a supported audio source.'
            );
          }
        }
      }

      if (!screenStream) {
        throw new Error('Failed to obtain screen stream');
      }

      // Check if we actually got audio tracks
      const audioTracks = screenStream.getAudioTracks();
      const videoTracks = screenStream.getVideoTracks();

      console.log(
        `Screen capture result: ${audioTracks.length} audio tracks, ${videoTracks.length} video tracks`
      );

      if (audioTracks.length === 0) {
        screenStream.getTracks().forEach((track) => track.stop());

        // Provide specific guidance based on what was captured
        const userAgent = navigator.userAgent;
        const isChrome = userAgent.includes('Chrome');
        const isFirefox = userAgent.includes('Firefox');

        let errorMessage = 'No audio track available in screen share. ';

        if (videoTracks.length > 0) {
          // User successfully shared screen but didn't enable audio
          errorMessage +=
            '🎯 You successfully shared your screen, but audio sharing was not enabled.\n\n';

          if (isChrome) {
            errorMessage +=
              '📋 In Chrome, when the sharing dialog appears:\n' +
              '1. Select the browser tab you want (that is playing audio)\n' +
              '2. Look for "Share tab audio" checkbox and CHECK IT ✓\n' +
              '3. OR select "Entire screen" and CHECK "Share system audio" ✓\n\n' +
              '💡 Tip: The audio checkbox is separate from selecting the source!';
          } else if (isFirefox) {
            errorMessage +=
              '📋 In Firefox, when the sharing dialog appears:\n' +
              '1. Select the screen/window you want\n' +
              '2. Look for "Share audio" checkbox and CHECK IT ✓\n\n' +
              '💡 Tip: Make sure the source is actually playing audio!';
          } else {
            errorMessage +=
              '📋 When the sharing dialog appears:\n' +
              '1. Look for an audio sharing checkbox\n' +
              '2. Make sure to CHECK the audio option ✓\n' +
              '3. Select a source that is playing audio\n\n' +
              '💡 Try using Chrome or Firefox for better support';
          }
        } else {
          // No video or audio tracks - complete failure
          if (isChrome) {
            errorMessage +=
              'Please try again and make sure to:\n' +
              '• Select a browser tab and check "Share tab audio"\n' +
              '• OR select entire screen and check "Share system audio"\n' +
              '• Make sure the selected source is actually playing audio';
          } else if (isFirefox) {
            errorMessage +=
              'Please try again and make sure to:\n' +
              '• Check the "Share audio" option in Firefox sharing dialog\n' +
              '• Select a source that is actually playing audio';
          } else {
            errorMessage +=
              'Please try again and:\n' +
              '• Look for audio sharing options in the dialog\n' +
              '• Select a source that is playing audio\n' +
              '• Try using Chrome or Firefox for better compatibility';
          }
        }

        throw new Error(errorMessage);
      }

      // Verify audio track is actually active
      const audioTrack = audioTracks[0];
      if (audioTrack.readyState === 'ended') {
        screenStream.getTracks().forEach((track) => track.stop());
        throw new Error(
          'Audio track ended immediately. The selected source may not have active audio.'
        );
      }

      speakerMediaStreamRef.current = screenStream;

      setState((prev) => ({
        ...prev,
        dualStream: {
          ...prev.dualStream!,
          isCapturingScreen: true,
          speakerConnectionState: 'connected',
        },
      }));

      // Handle stream end (user stops sharing)
      screenStream.getTracks().forEach((track) => {
        track.addEventListener('ended', () => {
          console.log('Screen sharing ended by user');
          stopScreenCapture();
        });
      });

      // Monitor audio track state
      audioTrack.addEventListener('ended', () => {
        console.log('Audio track ended');
        toast.warning('Screen audio capture stopped');
      });

      console.log(
        'Screen audio capture started successfully with',
        audioTracks.length,
        'audio track(s)'
      );
      toast.success(`Screen audio capture started! Capturing ${audioTracks.length} audio track(s)`);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to capture screen audio';

      // Handle user cancellation differently - don't show error state
      if (errorMessage === 'USER_CANCELLED_SCREEN_SHARING') {
        setState((prev) => ({
          ...prev,
          dualStream: {
            ...prev.dualStream!,
            speakerConnectionState: 'disconnected',
            speakerError: null,
          },
        }));

        console.log('User cancelled screen sharing dialog');
        throw error; // Re-throw to be handled by handleStartTranscription
      }

      // For other errors, show error state and detailed guidance
      setState((prev) => ({
        ...prev,
        dualStream: {
          ...prev.dualStream!,
          speakerConnectionState: 'error',
          speakerError: errorMessage,
        },
      }));

      console.error('Screen capture error:', error);

      // Show detailed error to user
      const lines = errorMessage.split('\n');
      toast.error(lines[0], {
        duration: 8000, // Longer duration for detailed instructions
      });

      // Log additional lines to console for more details
      if (lines.length > 1) {
        console.log('Additional guidance:');
        lines.slice(1).forEach((line) => console.log(line));
      }

      throw error;
    }
  }, [stopScreenCapture]);

  const handleStartTranscription = useCallback(
    async (config: LiveTranscriptionConfig) => {
      try {
        setState((prev) => ({
          ...prev,
          isDualMode: true, // Always use dual mode by default
          error: null,
          connectionState: 'connecting',
          dualStream: {
            ...prev.dualStream!,
            speakerConnectionState: 'connecting',
            microphoneConnectionState: 'connecting',
          },
        }));

        // Get JWT tokens for both streams
        const [speakerJwtResponse, microphoneJwtResponse] = await Promise.all([
          getLiveTranscriptionJwt({}).unwrap(),
          getLiveTranscriptionJwt({}).unwrap(),
        ]);

        if (!speakerJwtResponse.jwt || !microphoneJwtResponse.jwt) {
          throw new Error('Failed to get JWT tokens');
        }

        // Setup microphone transcription first (more reliable)
        await setupDualWebSocket(microphoneJwtResponse.jwt, config, 'microphone');
        await setupAudioCapture();

        // Try to setup speaker transcription (tab audio)
        let speakerCaptureSuccess = false;
        try {
          await setupDualWebSocket(speakerJwtResponse.jwt, config, 'speaker');
          await startScreenCapture();

          // Set up speaker audio processing if screen stream is available
          if (speakerMediaStreamRef.current) {
            await setupSpeakerAudioProcessing();
            speakerCaptureSuccess = true;
          }
        } catch (speakerError) {
          console.warn('Tab audio capture failed, continuing with microphone only:', speakerError);

          // Check if user cancelled screen sharing - if so, reset everything
          if (
            speakerError instanceof Error &&
            speakerError.message === 'USER_CANCELLED_SCREEN_SHARING'
          ) {
            console.log('User cancelled screen sharing, resetting to default state');

            // Clean up any partial connections
            if (speakerWebsocketRef.current) {
              speakerWebsocketRef.current.close();
              speakerWebsocketRef.current = null;
            }
            if (microphoneWebsocketRef.current) {
              microphoneWebsocketRef.current.close();
              microphoneWebsocketRef.current = null;
            }

            // Cleanup audio resources
            cleanupAudioResources();
            cleanupWebSocket();

            // Reset state to default
            setState((prev) => ({
              ...prev,
              isDualMode: false,
              error: null,
              connectionState: 'disconnected',
              isRecording: false,
              isTranscribing: false,
              isConnected: false,
              dualStream: {
                ...prev.dualStream!,
                isCapturingScreen: false,
                speakerConnected: false,
                microphoneConnected: false,
                speakerConnectionState: 'disconnected',
                microphoneConnectionState: 'disconnected',
                speakerError: null,
                microphoneError: null,
              },
            }));

            // Show user-friendly message
            toast.info('Screen sharing cancelled. Click "Start Recording" to try again.');
            return; // Exit early, don't continue with recording
          }

          // For other errors, continue with microphone-only recording
          // Update state to reflect speaker failure but don't fail completely
          setState((prev) => ({
            ...prev,
            dualStream: {
              ...prev.dualStream!,
              speakerConnectionState: 'error',
              speakerError:
                speakerError instanceof Error ? speakerError.message : 'Tab audio capture failed',
            },
          }));

          // Close speaker WebSocket if it was opened
          if (speakerWebsocketRef.current) {
            speakerWebsocketRef.current.close();
            speakerWebsocketRef.current = null;
          }
        }

        setState((prev) => ({
          ...prev,
          isRecording: true,
          isTranscribing: true,
          connectionState: 'connected',
        }));

        if (speakerCaptureSuccess) {
          toast.success('Live transcription started (capturing all audio)');
        } else {
          toast.warning('Live transcription started (microphone only - tab audio not available)');
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to start transcription';

        setState((prev) => ({
          ...prev,
          isDualMode: false,
          error: errorMessage,
          connectionState: 'error',
          isRecording: false,
          isTranscribing: false,
          dualStream: {
            ...prev.dualStream!,
            speakerConnectionState: 'error',
            microphoneConnectionState: 'error',
            speakerError: errorMessage,
            microphoneError: errorMessage,
          },
        }));

        // Clean up any partial connections
        if (speakerWebsocketRef.current) {
          speakerWebsocketRef.current.close();
          speakerWebsocketRef.current = null;
        }
        if (microphoneWebsocketRef.current) {
          microphoneWebsocketRef.current.close();
          microphoneWebsocketRef.current = null;
        }

        // Cleanup audio resources
        cleanupAudioResources();
        cleanupWebSocket();

        toast.error(`Failed to start transcription: ${errorMessage}`);
        throw error;
      }
    },
    [
      getLiveTranscriptionJwt,
      setupDualWebSocket,
      startScreenCapture,
      setupAudioCapture,
      setupSpeakerAudioProcessing,
      cleanupAudioResources,
      cleanupWebSocket,
    ]
  );

  const handleStopTranscription = useCallback(async () => {
    try {
      // Send EndOfStream messages to both WebSockets if they exist
      if (microphoneWebsocketRef.current?.readyState === WebSocket.OPEN) {
        flushAudioBuffer();
        const endMessage = {
          message: 'EndOfStream',
          last_seq_no: 1,
        };
        microphoneWebsocketRef.current.send(JSON.stringify(endMessage));
      }

      if (speakerWebsocketRef.current?.readyState === WebSocket.OPEN) {
        flushSpeakerAudioBuffer();
        const endMessage = {
          message: 'EndOfStream',
          last_seq_no: 1,
        };
        speakerWebsocketRef.current.send(JSON.stringify(endMessage));
      }

      // Also handle legacy single WebSocket
      if (websocketRef.current?.readyState === WebSocket.OPEN) {
        flushAudioBuffer();
        const endMessage = {
          message: 'EndOfStream',
          last_seq_no: 1,
        };
        websocketRef.current.send(JSON.stringify(endMessage));
      }

      // Cleanup audio resources
      cleanupAudioResources();

      // Stop screen capture if active
      if (speakerMediaStreamRef.current) {
        speakerMediaStreamRef.current.getTracks().forEach((track) => track.stop());
        speakerMediaStreamRef.current = null;
      }

      // Close WebSockets after a short delay
      setTimeout(() => {
        cleanupWebSocket();
        if (speakerWebsocketRef.current) {
          speakerWebsocketRef.current.close();
          speakerWebsocketRef.current = null;
        }
        if (microphoneWebsocketRef.current) {
          microphoneWebsocketRef.current.close();
          microphoneWebsocketRef.current = null;
        }
      }, 500);

      setState((prev) => ({
        ...prev,
        isRecording: false,
        isTranscribing: false,
        connectionState: 'disconnected',
        isConnected: false,
        isDualMode: false,
        dualStream: {
          ...prev.dualStream!,
          isCapturingScreen: false,
          speakerConnected: false,
          microphoneConnected: false,
          speakerConnectionState: 'disconnected',
          microphoneConnectionState: 'disconnected',
          speakerError: null,
          microphoneError: null,
        },
      }));

      toast.success('Live transcription stopped');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop transcription';

      setState((prev) => ({
        ...prev,
        error: errorMessage,
      }));

      // Force cleanup on error
      cleanupAudioResources();
      cleanupWebSocket();

      toast.error(`Failed to stop transcription: ${errorMessage}`);
      throw error;
    }
  }, [cleanupAudioResources, cleanupWebSocket, flushAudioBuffer, flushSpeakerAudioBuffer]);

  const clearTranscription = useCallback(() => {
    segmentsRef.current = [];
    setState((prev) => ({
      ...prev,
      segments: [],
      error: null,
    }));
  }, []);

  const exportTranscription = useCallback(() => {
    const finalSegments = segmentsRef.current.filter((segment) => segment.isFinal);
    return finalSegments
      .map((segment) => segment.text)
      .filter((text) => text.trim())
      .join(' ');
  }, []);

  const askForMicrophonePermission = useCallback(
    (silently = true) => {
      if (audioPermissionGrant.current) {
        return Promise.resolve(true);
      }
      return new Promise<boolean>((resolve) => {
        navigator.mediaDevices
          .getUserMedia({ audio: audioConstraints })
          .then(() => {
            audioPermissionGrant.current = true;
            resolve(true);
          })
          .catch((_error) => {
            audioPermissionGrant.current = false;
            if (!silently)
              toast.error(
                'Microphone access denied. Please allow microphone access in your browser settings.'
              );
            resolve(false);
          });
      });
    },
    [audioConstraints]
  );

  const switchAudioDevice = useCallback(
    async (deviceId: string) => {
      if (!audioPermissionGrant.current) {
        const granted = await askForMicrophonePermission(false);
        if (!granted) {
          throw new Error('Microphone access denied');
        }
      }

      setSelectedAudioDeviceId(deviceId);

      try {
        const wasRecording = state.isRecording;

        // If we're currently recording, we need to reconnect the audio pipeline
        if (wasRecording && audioContextRef.current) {
          // Disconnect current audio source but keep the WebSocket connection
          if (sourceRef.current) {
            sourceRef.current.disconnect();
            sourceRef.current = null;
          }

          // Stop current media stream
          if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach((track) => track.stop());
            mediaStreamRef.current = null;
          }

          // Get new media stream with selected device
          const constraints = {
            ...audioConstraints,
            deviceId: { exact: deviceId },
          };

          const stream = await navigator.mediaDevices.getUserMedia({ audio: constraints });
          mediaStreamRef.current = stream;

          // Reconnect audio processing pipeline
          const source = audioContextRef.current.createMediaStreamSource(stream);
          sourceRef.current = source;

          // Reconnect to existing worklet or processor
          if (workletNodeRef.current) {
            source.connect(workletNodeRef.current);
          } else if (processorRef.current) {
            source.connect(processorRef.current);
          }

          console.log('Audio device switched and reconnected to processing pipeline');
        } else {
          // Not recording, just switch the device for future use
          if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach((track) => track.stop());
            mediaStreamRef.current = null;
          }

          const constraints = {
            ...audioConstraints,
            deviceId: { exact: deviceId },
          };

          const stream = await navigator.mediaDevices.getUserMedia({ audio: constraints });
          mediaStreamRef.current = stream;

          // Stop the test stream since we're not recording
          stream.getTracks().forEach((track) => track.stop());
          mediaStreamRef.current = null;
        }

        // Update devices list
        const devices = await navigator.mediaDevices.enumerateDevices();
        setAudioDevices(devices.filter((device) => device.kind === 'audioinput'));

        toast.success('Audio device switched successfully');
      } catch (error) {
        console.error('Error switching audio device:', error);
        toast.error('Failed to switch audio device');
        throw new Error('Failed to switch audio device');
      }
    },
    [askForMicrophonePermission, audioConstraints, state.isRecording]
  );

  const bootstrap = useCallback(async () => {
    try {
      // Request microphone permissions
      const stream = await navigator.mediaDevices.getUserMedia({ audio: audioConstraints });
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputDevices = devices.filter((device) => device.kind === 'audioinput');

      setAudioDevices(audioInputDevices);
      audioPermissionGrant.current = true;
      stream.getTracks().forEach((track) => track.stop());

      // Set default audio device if available
      const defaultDeviceId = stream.getAudioTracks()[0]?.getSettings().deviceId || '';
      if (defaultDeviceId) {
        setSelectedAudioDeviceId(defaultDeviceId);
      }
    } catch {
      audioPermissionGrant.current = false;
      askForMicrophonePermission(false);
    }
  }, [askForMicrophonePermission, audioConstraints]);

  // Get current audio level for monitoring
  const getAudioLevel = useCallback(() => audioLevelRef.current, []);

  // Get performance metrics
  const getPerformanceMetrics = useCallback(
    () => ({
      audioPacketsSent: performanceMetricsRef.current.audioPacketsSent,
      audioPacketsLost: performanceMetricsRef.current.audioPacketsLost,
      averageLatency: performanceMetricsRef.current.averageLatency,
    }),
    []
  );

  // Get quality metrics
  const getQualityMetrics = useCallback(
    () => ({
      isSpeaking: qualityMetricsRef.current.isSpeaking,
      backgroundNoiseLevel: qualityMetricsRef.current.backgroundNoiseLevel,
    }),
    []
  );

  // Dual stream methods
  const getSegmentsBySource = useCallback((source?: AudioSource) => {
    if (!source) return segmentsRef.current;
    return segmentsRef.current.filter((segment) => segment.audioSource === source);
  }, []);

  const exportTranscriptionBySource = useCallback(
    (source?: AudioSource) => {
      const segments = getSegmentsBySource(source);
      const finalSegments = segments.filter((segment) => segment.isFinal);
      return finalSegments
        .map((segment) => segment.text)
        .filter((text) => text.trim())
        .join(' ');
    },
    [getSegmentsBySource]
  );

  return {
    state,
    audioDevices,
    selectedAudioDeviceId,
    startTranscription: handleStartTranscription,
    stopTranscription: handleStopTranscription,
    clearTranscription,
    exportTranscription,
    bootstrap,
    switchAudioDevice,
    getAudioLevel,
    getPerformanceMetrics,
    getQualityMetrics,
    getSegmentsBySource,
    exportTranscriptionBySource,
  };
};
